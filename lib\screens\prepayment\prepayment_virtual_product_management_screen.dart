import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/prepayment_virtual_product.dart';
import '../../providers/prepayment_virtual_product_provider.dart';
import '../../providers/prepayment_provider.dart';
import '../../utils/logger_utils.dart';
import '../../utils/toast_utils.dart';

class PrepaymentVirtualProductManagementScreen extends ConsumerStatefulWidget {
  const PrepaymentVirtualProductManagementScreen({super.key});

  @override
  ConsumerState<PrepaymentVirtualProductManagementScreen> createState() =>
      _PrepaymentVirtualProductManagementScreenState();
}

class _PrepaymentVirtualProductManagementScreenState
    extends ConsumerState<PrepaymentVirtualProductManagementScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // 빌드 완료 후 데이터 로드
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadVirtualProducts();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadVirtualProducts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(context, '가상 상품 목록을 불러오는 중 오류가 발생했습니다: $e');
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final virtualProductsState = ref.watch(prepaymentVirtualProductNotifierProvider);
    final virtualProducts = virtualProductsState.virtualProducts;

    // 검색 필터링
    final filteredProducts = virtualProducts.where((product) =>
        product.name.toLowerCase().contains(_searchQuery.toLowerCase())).toList();

    return Scaffold(
      appBar: AppBar(
        title: const Text('선입금 상품 관리'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadVirtualProducts,
            tooltip: '새로고침',
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 검색 필드
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                labelText: '상품명 검색',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          // 상품 목록
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : filteredProducts.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                        itemCount: filteredProducts.length,
                        itemBuilder: (context, index) {
                          final product = filteredProducts[index];
                          return _buildProductCard(product);
                        },
                      ),
          ),
        ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _searchQuery.isEmpty ? Icons.inventory : Icons.search_off,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isEmpty ? '등록된 가상 상품이 없습니다' : '검색 결과가 없습니다',
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontSize: 16, color: Colors.grey),
          ),
          if (_searchQuery.isEmpty) ...[
            const SizedBox(height: 8),
            const Text(
              '선입금을 등록하면 자동으로 가상 상품이 생성됩니다',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProductCard(PrepaymentVirtualProduct product) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
      child: ListTile(
        title: Text(
          product.name,
          style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontFamily: 'Pretendard', fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('총 판매 수: ${product.quantity}개'),
            Text('생성일: ${_formatDate(product.createdAt)}'),
            if (product.updatedAt != null)
              Text('수정일: ${_formatDate(product.updatedAt!)}'),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _showEditDialog(product);
                break;
              case 'delete':
                _showDeleteDialog(product);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 20),
                  SizedBox(width: 8),
                  Text('수정'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, size: 20, color: Colors.red),
                  SizedBox(width: 8),
                  Text('삭제', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  void _showEditDialog(PrepaymentVirtualProduct product) {
    final nameController = TextEditingController(text: product.name);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('가상 상품 수정'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: '상품명',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () async {
              final newName = nameController.text.trim();

              if (newName.isEmpty) {
                ToastUtils.showError(context, '상품명을 입력해주세요');
                return;
              }

              try {
                // 가상 상품 이름 변경 시 모든 관련 선입금 데이터도 함께 업데이트
                await _updateVirtualProductName(product.name, newName);
                
                Navigator.of(context).pop();
                ToastUtils.showSuccess(context, '가상 상품이 수정되었습니다');
              } catch (e) {
                ToastUtils.showError(context, '수정 중 오류가 발생했습니다: $e');
              }
            },
            child: const Text('수정'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(PrepaymentVirtualProduct product) async {
    final shouldDelete = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        icon: const Icon(Icons.delete_forever, color: Colors.red),
        title: const Text('가상 상품 삭제'),
        content: Text('정말로 "${product.name}" 상품을 삭제하시겠습니까?\n\n이 작업은 되돌릴 수 없습니다.'),
        actions: [
          TextButton(
            onPressed: () {
              if (mounted) {
                Navigator.of(context).pop(false);
              }
            },
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () {
              if (mounted) {
                Navigator.of(context).pop(true);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('삭제'),
          ),
        ],
      ),
    );

    if (shouldDelete == true) {
      try {
        // 가상 상품 삭제 시 모든 관련 선입금 데이터에서도 해당 상품 제거
        await _deleteVirtualProduct(product.name);

        if (mounted) {
          ToastUtils.showSuccess(context, '가상 상품이 삭제되었습니다');
        }
      } catch (e) {
        if (mounted) {
          ToastUtils.showError(context, '삭제 중 오류가 발생했습니다: $e');
        }
      }
    }
  }

  Future<void> _updateVirtualProductName(String oldName, String newName) async {
    LoggerUtils.logInfo('가상 상품 이름 변경: $oldName -> $newName', tag: 'VirtualProductManagement');

    // 1. 모든 선입금 데이터에서 해당 상품명 변경
    final prepaymentNotifier = ref.read(prepaymentNotifierProvider.notifier);
    final prepayments = ref.read(prepaymentNotifierProvider).prepayments;

    for (final prepayment in prepayments) {
      bool hasChanges = false;
      final updatedProducts = <Map<String, dynamic>>[];

      for (final product in prepayment.purchasedProducts) {
        if (product.name == oldName) {
          // 상품명이 일치하면 새 이름으로 변경
          updatedProducts.add({
            'name': newName,
            'quantity': product.quantity,
            'price': product.price,
          });
          hasChanges = true;
        } else {
          // 다른 상품은 그대로 유지
          updatedProducts.add({
            'name': product.name,
            'quantity': product.quantity,
            'price': product.price,
          });
        }
      }

      if (hasChanges) {
        // 선입금 데이터 업데이트
        final updatedPrepayment = prepayment.copyWith(
          purchasedProductsJson: updatedProducts.toString(),
          productNameList: updatedProducts.map((p) => p['name'] as String).join(', '),
        );
        await prepaymentNotifier.updatePrepayment(updatedPrepayment);
      }
    }

    // 2. 가상 상품 데이터 갱신
    await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();

    LoggerUtils.logInfo('가상 상품 이름 변경 완료', tag: 'VirtualProductManagement');
  }

  Future<void> _deleteVirtualProduct(String productName) async {
    LoggerUtils.logInfo('가상 상품 삭제: $productName', tag: 'VirtualProductManagement');

    // 1. 모든 선입금 데이터에서 해당 상품 제거
    final prepaymentNotifier = ref.read(prepaymentNotifierProvider.notifier);
    final prepayments = ref.read(prepaymentNotifierProvider).prepayments;

    for (final prepayment in prepayments) {
      bool hasChanges = false;
      final updatedProducts = <Map<String, dynamic>>[];

      for (final product in prepayment.purchasedProducts) {
        if (product.name != productName) {
          // 삭제할 상품이 아닌 경우만 유지
          updatedProducts.add({
            'name': product.name,
            'quantity': product.quantity,
            'price': product.price,
          });
        } else {
          hasChanges = true;
        }
      }

      if (hasChanges) {
        // 선입금 데이터 업데이트
        final updatedPrepayment = prepayment.copyWith(
          purchasedProductsJson: updatedProducts.toString(),
          productNameList: updatedProducts.map((p) => p['name'] as String).join(', '),
        );
        await prepaymentNotifier.updatePrepayment(updatedPrepayment);
      }
    }

    // 2. 가상 상품 데이터 갱신
    await ref.read(prepaymentVirtualProductNotifierProvider.notifier).loadVirtualProducts();

    LoggerUtils.logInfo('가상 상품 삭제 완료', tag: 'VirtualProductManagement');
  }
} 

